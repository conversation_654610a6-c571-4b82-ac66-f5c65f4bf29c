import pygame
import math
import copy
import os 
import json
import tkinter as tk
from tkinter import filedialog
from customer_info import start_customer_info
from view_3d import launch_3d_viewer

# --- ثوابت وإعدادات Pygame ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
SIDEBAR_WIDTH = 150
DRAWING_AREA_WIDTH = SCREEN_WIDTH - SIDEBAR_WIDTH
INITIAL_PIXELS_PER_METER = 50
MIN_PIXELS_PER_METER = 10
MAX_PIXELS_PER_METER = 300
ZOOM_FACTOR_STEP = 1.1

# --- الألوان ---
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (200, 200, 200)
BLUE = (0, 0, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
DARK_GRAY = (50, 50, 50)
DOOR_COLOR = (150, 75, 0)
WINDOW_COLOR = (100, 150, 255)
HIGHLIGHT_COLOR = (255, 165, 0)
INPUT_BOX_COLOR = (230, 230, 230)
INPUT_BOX_ACTIVE_COLOR = (255, 255, 200)
DELETE_COLOR = (200, 0, 0)
GRID_COLOR = (230, 230, 230)
RULER_COLOR = (0, 150, 150)
LIGHT_BLUE = (173, 216, 230)
DARK_BLUE = (0, 0, 139)

# --- متغيرات التراجع ---
history = []
MAX_HISTORY_SIZE = 20

# --- الأدوات وعناصر واجهة المستخدم ---
TOOLS = ["WALL", "DOOR", "WINDOW", "DELETE", "MEASURE", "SAVE"]
TOOL_RECTS = []
INPUT_RECTS = {}

# --- قيم افتراضية لحقول الإدخال ---
DEFAULT_DOOR_WIDTH_M_STR = "0.9"
DEFAULT_WINDOW_WIDTH_M_STR = "1.2"

# --- تعريف أنواع الأبواب واتجاهات الفتح ---
DOOR_TYPES = ["SINGLE", "DOUBLE", "SLIDING", "FOLDING"]
DOOR_SWING_DIRECTIONS = ["LEFT", "RIGHT"]
DEFAULT_DOOR_TYPE = "SINGLE"
DEFAULT_DOOR_SWING = "LEFT"

# --- دالة بدء تطبيق الرسم ---
def start_drawing_app():
    # --- تهيئة Pygame ---
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("MEASURE")
    font = pygame.font.Font(None, 24)
    small_font = pygame.font.Font(None, 18)
    input_font = pygame.font.Font(None, 22)
    clock = pygame.time.Clock()
    
    # --- تحميل الأيقونات ---
    ICON_SIZE = (40, 40)
    icons = {}
    def load_icon(filename):
        try:
            img = pygame.image.load(os.path.join(filename)).convert_alpha()
            return pygame.transform.scale(img, ICON_SIZE)
        except pygame.error as e:
            print(f"خطأ في تحميل الأيقونة {filename}: {e}")
            fallback_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
            fallback_surface.fill((0,0,0,0))
            pygame.draw.rect(fallback_surface, RED, (0, 0, ICON_SIZE[0], ICON_SIZE[1]), 1)
            pygame.draw.line(fallback_surface, RED, (0,0), ICON_SIZE, 1)
            pygame.draw.line(fallback_surface, RED, (ICON_SIZE[0],0), (0, ICON_SIZE[1]), 1)
            return fallback_surface

    try:
        icons["WALL"] = load_icon("wall_icon.png")
        icons["DOOR"] = load_icon("door_icon.png")
        icons["WINDOW"] = load_icon("window_icon.png")
        icons["DELETE"] = load_icon("delete_icon.png")
        icons["MEASURE"] = load_icon("measure_icon.png")
        icons["SAVE"] = load_icon("save_icon.png")
    except Exception as e:
        print(f"حدث خطأ عام أثناء تحميل الأيقونات: {e}")

    # --- متغيرات الحالة ---
    running = True
    current_tool = None
    drawing = False
    start_pos_m = None
    elements = []
    measurements = []

    # --- متغيرات التكبير/التصغير ---
    pixels_per_meter = INITIAL_PIXELS_PER_METER
    view_offset_x = 0
    view_offset_y = 0

    # --- متغيرات حقول الإدخال ---
    active_input = None
    input_values = {
        'door': DEFAULT_DOOR_WIDTH_M_STR,
        'window': DEFAULT_WINDOW_WIDTH_M_STR,
        'door_type': DEFAULT_DOOR_TYPE,
        'door_swing': DEFAULT_DOOR_SWING,
        'wall_height': "3.0"  # إضافة قيمة افتراضية لارتفاع الجدار
    }

    # --- متغيرات المسطرة ---
    ruler_point1_m = None
    ruler_point2_m = None
    ruler_snapped_point_m = None

    # --- دوال التحويل ---
    def world_m_to_screen(world_m_pos):
        screen_x = round(world_m_pos[0] * pixels_per_meter + view_offset_x) + SIDEBAR_WIDTH
        screen_y = round(world_m_pos[1] * pixels_per_meter + view_offset_y)
        return (screen_x, screen_y)

    def screen_to_world_m(screen_pos):
        screen_x_adj = screen_pos[0]
        if screen_x_adj >= SIDEBAR_WIDTH:
            screen_x_adj -= SIDEBAR_WIDTH
        else:
            screen_x_adj = 0
        if pixels_per_meter == 0:
            return (0, 0)
        world_mx = (screen_x_adj - view_offset_x) / pixels_per_meter
        world_my = (screen_pos[1] - view_offset_y) / pixels_per_meter
        return (world_mx, world_my)

    # --- دوال مساعدة ---
    def draw_sidebar(selected_tool, current_input_values, active_input_field):
        sidebar_area = pygame.Rect(0, 0, SIDEBAR_WIDTH, SCREEN_HEIGHT)
        pygame.draw.rect(screen, GRAY, sidebar_area)

        TOOL_RECTS.clear()
        INPUT_RECTS.clear()

        y_offset = 20
        button_height = 55
        input_height = 25
        button_padding = 15

        scale_text = small_font.render(f"Scale: {pixels_per_meter:.1f} px/m", True, BLACK)
        screen.blit(scale_text, (10, SCREEN_HEIGHT - 60))
        zoom_instr_text = small_font.render("Wheel to zoom.", True, BLACK)
        screen.blit(zoom_instr_text, (10, SCREEN_HEIGHT - 40))
        ruler_instr_text = small_font.render("Select tool above.", True, BLACK)
        screen.blit(ruler_instr_text, (10, SCREEN_HEIGHT - 20))

        for tool in TOOLS:
            button_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, button_height)
            TOOL_RECTS.append(button_rect)

            bg_color = BLUE
            if tool == selected_tool:
                bg_color = HIGHLIGHT_COLOR
            elif tool == "DELETE":
                bg_color = DELETE_COLOR
            elif tool == "MEASURE":
                 bg_color = RULER_COLOR
            elif tool == "SAVE":
                 bg_color = GREEN

            pygame.draw.rect(screen, bg_color, button_rect, border_radius=5)
            icon_surface = icons.get(tool)
            if icon_surface:
                icon_rect = icon_surface.get_rect(center=button_rect.center)
                screen.blit(icon_surface, icon_rect)
            else:
                tool_text = font.render(tool, True, WHITE)
                text_rect = tool_text.get_rect(center=button_rect.center)
                screen.blit(tool_text, text_rect)

            y_offset += button_height + button_padding

            if tool == "DOOR":
                # مربع إدخال عرض الباب
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['door'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'door' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("W: " + current_input_values['door'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5

            elif tool == "WINDOW":
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['window'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'window' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("W: " + current_input_values['window'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5

        # إضافة زر العرض ثلاثي الأبعاد في نهاية الشريط الجانبي
        view_3d_btn_rect = pygame.Rect(10, SCREEN_HEIGHT - 60, SIDEBAR_WIDTH - 20, 40)
        pygame.draw.rect(screen, LIGHT_BLUE, view_3d_btn_rect, 0, 5)
        pygame.draw.rect(screen, DARK_BLUE, view_3d_btn_rect, 2, 5)
        view_3d_text = font.render("عرض ثلاثي الأبعاد", True, DARK_BLUE)
        text_rect = view_3d_text.get_rect(center=view_3d_btn_rect.center)
        screen.blit(view_3d_text, text_rect)

        # إضافة حقل إدخال لارتفاع الجدار إذا كانت أداة الجدار محددة
        if selected_tool == "WALL":
            wall_height_label = small_font.render("ارتفاع الجدار (م):", True, BLACK)
            screen.blit(wall_height_label, (10, y_offset + button_height + 10))

            wall_height_rect = pygame.Rect(10, y_offset + button_height + 30, SIDEBAR_WIDTH - 20, input_height)
            INPUT_RECTS['wall_height'] = wall_height_rect

            if active_input_field == 'wall_height':
                pygame.draw.rect(screen, INPUT_BOX_ACTIVE_COLOR, wall_height_rect)
            else:
                pygame.draw.rect(screen, INPUT_BOX_COLOR, wall_height_rect)

            pygame.draw.rect(screen, BLACK, wall_height_rect, 1)
            wall_height_text = input_font.render(current_input_values.get('wall_height', "3.0"), True, BLACK)
            screen.blit(wall_height_text, (wall_height_rect.x + 5, wall_height_rect.y + 5))

        return view_3d_btn_rect  # إرجاع مستطيل الزر للتحقق من النقر عليه

    def draw_grid():
        grid_spacing_m = 1.0
        grid_spacing_px = grid_spacing_m * pixels_per_meter

        if grid_spacing_px < 10:
            return

        start_x = int(-view_offset_x % grid_spacing_px)
        start_y = int(-view_offset_y % grid_spacing_px)

        for x in range(start_x + SIDEBAR_WIDTH, SCREEN_WIDTH, int(grid_spacing_px)):
            pygame.draw.line(screen, GRID_COLOR, (x, 0), (x, SCREEN_HEIGHT), 1)

        for y in range(start_y, SCREEN_HEIGHT, int(grid_spacing_px)):
            pygame.draw.line(screen, GRID_COLOR, (SIDEBAR_WIDTH, y), (SCREEN_WIDTH, y), 1)

    def draw_elements():
        for elem in elements:
            if elem['type'] == 'wall':
                start_screen = world_m_to_screen(elem['start_m'])
                end_screen = world_m_to_screen(elem['end_m'])
                pygame.draw.line(screen, BLACK, start_screen, end_screen, 3)

                # رسم نقاط النهاية
                pygame.draw.circle(screen, BLACK, start_screen, 4)
                pygame.draw.circle(screen, BLACK, end_screen, 4)

                # عرض طول الجدار
                mid_x = (start_screen[0] + end_screen[0]) // 2
                mid_y = (start_screen[1] + end_screen[1]) // 2
                length_text = small_font.render(f"{elem['length_m']:.2f}m", True, BLACK)
                screen.blit(length_text, (mid_x, mid_y - 10))

            elif elem['type'] == 'door':
                start_screen = world_m_to_screen(elem['start_m'])
                end_screen = world_m_to_screen(elem['end_m'])
                pygame.draw.line(screen, DOOR_COLOR, start_screen, end_screen, 5)

                # رسم قوس الباب
                door_vec = (elem['end_m'][0] - elem['start_m'][0], elem['end_m'][1] - elem['start_m'][1])
                door_length = math.sqrt(door_vec[0]**2 + door_vec[1]**2)
                if door_length > 0:
                    door_unit = (door_vec[0] / door_length, door_vec[1] / door_length)
                    door_normal = (-door_unit[1], door_unit[0])

                    arc_center_m = elem['start_m']
                    arc_end_m = (elem['end_m'][0] + door_normal[0] * door_length,
                               elem['end_m'][1] + door_normal[1] * door_length)

                    arc_center_screen = world_m_to_screen(arc_center_m)
                    arc_end_screen = world_m_to_screen(arc_end_m)

                    pygame.draw.arc(screen, DOOR_COLOR,
                                  pygame.Rect(arc_center_screen[0] - int(door_length * pixels_per_meter),
                                            arc_center_screen[1] - int(door_length * pixels_per_meter),
                                            int(door_length * pixels_per_meter * 2),
                                            int(door_length * pixels_per_meter * 2)),
                                  0, math.pi/2, 2)

            elif elem['type'] == 'window':
                start_screen = world_m_to_screen(elem['start_m'])
                end_screen = world_m_to_screen(elem['end_m'])
                pygame.draw.line(screen, WINDOW_COLOR, start_screen, end_screen, 5)

                # رسم خطوط النافذة
                window_vec = (elem['end_m'][0] - elem['start_m'][0], elem['end_m'][1] - elem['start_m'][1])
                window_length = math.sqrt(window_vec[0]**2 + window_vec[1]**2)
                if window_length > 0:
                    window_unit = (window_vec[0] / window_length, window_vec[1] / window_length)
                    window_normal = (-window_unit[1], window_unit[0])

                    offset = 0.1
                    line1_start_m = (elem['start_m'][0] + window_normal[0] * offset,
                                   elem['start_m'][1] + window_normal[1] * offset)
                    line1_end_m = (elem['end_m'][0] + window_normal[0] * offset,
                                 elem['end_m'][1] + window_normal[1] * offset)

                    line2_start_m = (elem['start_m'][0] - window_normal[0] * offset,
                                   elem['start_m'][1] - window_normal[1] * offset)
                    line2_end_m = (elem['end_m'][0] - window_normal[0] * offset,
                                 elem['end_m'][1] - window_normal[1] * offset)

                    line1_start_screen = world_m_to_screen(line1_start_m)
                    line1_end_screen = world_m_to_screen(line1_end_m)
                    line2_start_screen = world_m_to_screen(line2_start_m)
                    line2_end_screen = world_m_to_screen(line2_end_m)

                    pygame.draw.line(screen, WINDOW_COLOR, line1_start_screen, line1_end_screen, 2)
                    pygame.draw.line(screen, WINDOW_COLOR, line2_start_screen, line2_end_screen, 2)

    def draw_measurements():
        for measurement in measurements:
            start_screen = world_m_to_screen(measurement['start_m'])
            end_screen = world_m_to_screen(measurement['end_m'])

            pygame.draw.line(screen, RULER_COLOR, start_screen, end_screen, 2)
            pygame.draw.circle(screen, RULER_COLOR, start_screen, 3)
            pygame.draw.circle(screen, RULER_COLOR, end_screen, 3)

            mid_x = (start_screen[0] + end_screen[0]) // 2
            mid_y = (start_screen[1] + end_screen[1]) // 2
            distance_text = font.render(f"{measurement['distance_m']:.2f}m", True, RULER_COLOR)
            screen.blit(distance_text, (mid_x, mid_y - 15))

    def save_all_data():
        """حفظ جميع البيانات إلى ملف JSON"""
        try:
            # تجميع بيانات الجدران
            walls_data = []
            for elem in elements:
                if elem['type'] == 'wall':
                    wall_data = {
                        'start_m': list(elem['start_m']),
                        'end_m': list(elem['end_m']),
                        'length_m': elem['length_m']
                    }
                    # إضافة ارتفاع الجدار إذا كان موجودًا
                    if 'height_m' in elem:
                        wall_data['height_m'] = elem['height_m']
                        print(f"حفظ جدار بارتفاع: {elem['height_m']} متر")
                    else:
                        # إضافة ارتفاع افتراضي إذا لم يكن موجودًا
                        wall_data['height_m'] = 3.0
                        print(f"حفظ جدار بارتفاع افتراضي: 3.0 متر")

                    walls_data.append(wall_data)

            # تجميع بيانات الأبواب
            doors_data = []
            for elem in elements:
                if elem['type'] == 'door':
                    doors_data.append({
                        'start_m': list(elem['start_m']),
                        'end_m': list(elem['end_m']),
                        'width_m': elem['width_m'],
                        'door_type': elem.get('door_type', 'SINGLE'),
                        'swing_direction': elem.get('swing_direction', 'LEFT')
                    })

            # تجميع بيانات النوافذ
            windows_data = []
            for elem in elements:
                if elem['type'] == 'window':
                    windows_data.append({
                        'start_m': list(elem['start_m']),
                        'end_m': list(elem['end_m']),
                        'width_m': elem['width_m']
                    })

            # تجميع بيانات القياسات
            measurements_data = []
            for measurement in measurements:
                measurements_data.append({
                    'start_m': list(measurement['start_m']),
                    'end_m': list(measurement['end_m']),
                    'distance_m': measurement['distance_m']
                })

            # إنشاء البيانات النهائية
            all_data = {
                'walls': walls_data,
                'doors': doors_data,
                'windows': windows_data,
                'measurements': measurements_data
            }

            # فتح مربع حوار لحفظ الملف
            root = tk.Tk()
            root.withdraw()
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="حفظ التصميم"
            )
            root.destroy()

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(all_data, f, indent=4, ensure_ascii=False)
                print(f"تم حفظ التصميم في: {file_path}")
                return True
            else:
                print("تم إلغاء عملية الحفظ")
                return False

        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False

    def add_to_history():
        """إضافة الحالة الحالية إلى التاريخ"""
        current_state = {
            'elements': copy.deepcopy(elements),
            'measurements': copy.deepcopy(measurements)
        }
        history.append(current_state)
        if len(history) > MAX_HISTORY_SIZE:
            history.pop(0)

    def create_wall(start_m, end_m):
        """إنشاء جدار جديد"""
        length_m = math.sqrt((end_m[0] - start_m[0])**2 + (end_m[1] - start_m[1])**2)

        # الحصول على ارتفاع الجدار من حقل الإدخال
        try:
            wall_height = float(input_values.get('wall_height', '3.0'))
        except ValueError:
            wall_height = 3.0  # قيمة افتراضية في حالة خطأ في التحويل

        wall = {
            'type': 'wall',
            'start_m': start_m,
            'end_m': end_m,
            'length_m': length_m,
            'height_m': wall_height  # إضافة ارتفاع الجدار
        }
        elements.append(wall)
        print(f"تم إنشاء جدار بطول {length_m:.2f} متر وارتفاع {wall_height:.2f} متر")

    # --- الحلقة الرئيسية ---
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                # التحقق من النقر على الشريط الجانبي
                if mouse_pos[0] < SIDEBAR_WIDTH:
                    # التحقق من النقر على الأدوات
                    for i, tool_rect in enumerate(TOOL_RECTS):
                        if tool_rect.collidepoint(mouse_pos):
                            current_tool = TOOLS[i]
                            if current_tool == "SAVE":
                                save_all_data()
                                current_tool = None
                            break

                    # التحقق من النقر على حقول الإدخال
                    clicked_input = None
                    for input_name, input_rect in INPUT_RECTS.items():
                        if input_rect.collidepoint(mouse_pos):
                            clicked_input = input_name
                            break
                    active_input = clicked_input

                    # التحقق من النقر على زر العرض ثلاثي الأبعاد
                    view_3d_btn_rect = draw_sidebar(current_tool, input_values, active_input)
                    if view_3d_btn_rect.collidepoint(mouse_pos):
                        print("تشغيل العرض ثلاثي الأبعاد...")
                        # حفظ التصميم الحالي مؤقتاً
                        temp_file = "temp_design.json"
                        walls_data = []
                        for elem in elements:
                            if elem['type'] == 'wall':
                                wall_data = {
                                    'start_m': list(elem['start_m']),
                                    'end_m': list(elem['end_m']),
                                    'length_m': elem['length_m'],
                                    'height_m': elem.get('height_m', 3.0)
                                }
                                walls_data.append(wall_data)

                        temp_data = {'walls': walls_data, 'measurements': []}
                        with open(temp_file, 'w', encoding='utf-8') as f:
                            json.dump(temp_data, f, indent=4, ensure_ascii=False)

                        # تشغيل العارض ثلاثي الأبعاد
                        launch_3d_viewer(temp_file)

                        # حذف الملف المؤقت
                        try:
                            os.remove(temp_file)
                        except:
                            pass

                else:
                    # النقر في منطقة الرسم
                    if event.button == 1:  # الزر الأيسر
                        if current_tool in ["WALL", "DOOR", "WINDOW", "MEASURE"]:
                            if not drawing:
                                start_pos_m = screen_to_world_m(mouse_pos)
                                drawing = True
                                add_to_history()
                            else:
                                end_pos_m = screen_to_world_m(mouse_pos)

                                if current_tool == "WALL":
                                    create_wall(start_pos_m, end_pos_m)
                                elif current_tool == "DOOR":
                                    try:
                                        door_width = float(input_values['door'])
                                        door = {
                                            'type': 'door',
                                            'start_m': start_pos_m,
                                            'end_m': end_pos_m,
                                            'width_m': door_width,
                                            'door_type': input_values['door_type'],
                                            'swing_direction': input_values['door_swing']
                                        }
                                        elements.append(door)
                                    except ValueError:
                                        print("خطأ: عرض الباب يجب أن يكون رقماً")
                                elif current_tool == "WINDOW":
                                    try:
                                        window_width = float(input_values['window'])
                                        window = {
                                            'type': 'window',
                                            'start_m': start_pos_m,
                                            'end_m': end_pos_m,
                                            'width_m': window_width
                                        }
                                        elements.append(window)
                                    except ValueError:
                                        print("خطأ: عرض النافذة يجب أن يكون رقماً")
                                elif current_tool == "MEASURE":
                                    distance = math.sqrt((end_pos_m[0] - start_pos_m[0])**2 +
                                                       (end_pos_m[1] - start_pos_m[1])**2)
                                    measurement = {
                                        'start_m': start_pos_m,
                                        'end_m': end_pos_m,
                                        'distance_m': distance
                                    }
                                    measurements.append(measurement)

                                drawing = False
                                start_pos_m = None

            elif event.type == pygame.KEYDOWN:
                if active_input:
                    if event.key == pygame.K_RETURN:
                        active_input = None
                    elif event.key == pygame.K_BACKSPACE:
                        if len(input_values[active_input]) > 0:
                            input_values[active_input] = input_values[active_input][:-1]
                    else:
                        if event.unicode.isprintable():
                            input_values[active_input] += event.unicode

            elif event.type == pygame.MOUSEWHEEL:
                if event.y > 0:
                    pixels_per_meter = min(pixels_per_meter * ZOOM_FACTOR_STEP, MAX_PIXELS_PER_METER)
                elif event.y < 0:
                    pixels_per_meter = max(pixels_per_meter / ZOOM_FACTOR_STEP, MIN_PIXELS_PER_METER)

        # --- الرسم ---
        screen.fill(WHITE)

        # رسم الشبكة
        draw_grid()

        # رسم العناصر
        draw_elements()

        # رسم القياسات
        draw_measurements()

        # رسم الخط المؤقت أثناء الرسم
        if drawing and start_pos_m:
            mouse_pos = pygame.mouse.get_pos()
            current_pos_m = screen_to_world_m(mouse_pos)
            start_screen = world_m_to_screen(start_pos_m)
            current_screen = world_m_to_screen(current_pos_m)

            if current_tool == "WALL":
                pygame.draw.line(screen, BLACK, start_screen, current_screen, 3)
            elif current_tool == "DOOR":
                pygame.draw.line(screen, DOOR_COLOR, start_screen, current_screen, 5)
            elif current_tool == "WINDOW":
                pygame.draw.line(screen, WINDOW_COLOR, start_screen, current_screen, 5)
            elif current_tool == "MEASURE":
                pygame.draw.line(screen, RULER_COLOR, start_screen, current_screen, 2)

        # رسم الشريط الجانبي
        view_3d_btn_rect = draw_sidebar(current_tool, input_values, active_input)

        pygame.display.flip()
        clock.tick(60)

    pygame.quit()

# --- دالة حفظ إلى ملف ---
def save_to_file(file_path, elements, measurements):
    """حفظ التصميم الحالي إلى ملف JSON"""
    try:
        # تجميع بيانات الجدران
        walls_data = []
        for elem in elements:
            if elem['type'] == 'wall':
                wall_data = {
                    'start_m': list(elem['start_m']),
                    'end_m': list(elem['end_m']),
                    'length_m': elem['length_m']
                }

                # إضافة ارتفاع الجدار إذا كان موجودًا
                if 'height_m' in elem:
                    wall_data['height_m'] = elem['height_m']
                    print(f"حفظ جدار بارتفاع: {elem['height_m']} متر")
                else:
                    # إضافة ارتفاع افتراضي إذا لم يكن موجودًا
                    wall_data['height_m'] = 3.0
                    print(f"حفظ جدار بارتفاع افتراضي: 3.0 متر")

                walls_data.append(wall_data)

        # تجميع بيانات الأبواب
        doors_data = []
        for elem in elements:
            if elem['type'] == 'door':
                doors_data.append({
                    'start_m': list(elem['start_m']),
                    'end_m': list(elem['end_m']),
                    'width_m': elem['width_m'],
                    'door_type': elem.get('door_type', 'SINGLE'),
                    'swing_direction': elem.get('swing_direction', 'LEFT')
                })

        # تجميع بيانات النوافذ
        windows_data = []
        for elem in elements:
            if elem['type'] == 'window':
                windows_data.append({
                    'start_m': list(elem['start_m']),
                    'end_m': list(elem['end_m']),
                    'width_m': elem['width_m']
                })

        # تجميع بيانات القياسات
        measurements_data = []
        for measurement in measurements:
            measurements_data.append({
                'start_m': list(measurement['start_m']),
                'end_m': list(measurement['end_m']),
                'distance_m': measurement['distance_m']
            })

        # إنشاء البيانات النهائية
        all_data = {
            'walls': walls_data,
            'doors': doors_data,
            'windows': windows_data,
            'measurements': measurements_data
        }

        # حفظ البيانات
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, indent=4, ensure_ascii=False)

        print(f"تم حفظ التصميم في: {file_path}")
        return True

    except Exception as e:
        print(f"خطأ في حفظ البيانات: {e}")
        return False

# --- نقطة البداية ---
if __name__ == "__main__":
    start_customer_info()
