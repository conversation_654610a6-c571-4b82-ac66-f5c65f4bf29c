#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونات بسيطة للتطبيق
"""

import pygame
import os

def create_simple_icons():
    """إنشاء أيقونات بسيطة للأدوات"""
    
    # تهيئة pygame
    pygame.init()
    
    # حجم الأيقونة
    ICON_SIZE = (40, 40)
    
    # ألوان
    WHITE = (255, 255, 255)
    BLACK = (0, 0, 0)
    RED = (255, 0, 0)
    BLUE = (0, 0, 255)
    GREEN = (0, 255, 0)
    BROWN = (150, 75, 0)
    GRAY = (128, 128, 128)
    
    # إنشاء أيقونة الجدار
    wall_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    wall_surface.fill((0, 0, 0, 0))  # شفاف
    pygame.draw.rect(wall_surface, BLACK, (5, 15, 30, 10), 3)
    pygame.draw.rect(wall_surface, GRAY, (7, 17, 26, 6))
    pygame.image.save(wall_surface, "wall_icon.png")
    
    # إنشاء أيقونة الباب
    door_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    door_surface.fill((0, 0, 0, 0))
    pygame.draw.rect(door_surface, BROWN, (10, 5, 20, 30), 0)
    pygame.draw.rect(door_surface, BLACK, (10, 5, 20, 30), 2)
    pygame.draw.circle(door_surface, BLACK, (25, 20), 2)
    pygame.image.save(door_surface, "door_icon.png")

    # إنشاء أيقونة النافذة
    window_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    window_surface.fill((0, 0, 0, 0))
    pygame.draw.rect(window_surface, BLUE, (8, 8, 24, 24), 3)
    pygame.draw.line(window_surface, BLUE, (20, 8), (20, 32), 2)
    pygame.draw.line(window_surface, BLUE, (8, 20), (32, 20), 2)
    pygame.image.save(window_surface, "window_icon.png")

    # إنشاء أيقونة الحذف
    delete_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    delete_surface.fill((0, 0, 0, 0))
    pygame.draw.line(delete_surface, RED, (10, 10), (30, 30), 4)
    pygame.draw.line(delete_surface, RED, (30, 10), (10, 30), 4)
    pygame.image.save(delete_surface, "delete_icon.png")

    # إنشاء أيقونة القياس
    measure_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    measure_surface.fill((0, 0, 0, 0))
    pygame.draw.line(measure_surface, BLACK, (5, 20), (35, 20), 3)
    pygame.draw.line(measure_surface, BLACK, (5, 15), (5, 25), 2)
    pygame.draw.line(measure_surface, BLACK, (35, 15), (35, 25), 2)
    pygame.image.save(measure_surface, "measure_icon.png")

    # إنشاء أيقونة الحفظ
    save_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
    save_surface.fill((0, 0, 0, 0))
    pygame.draw.rect(save_surface, BLACK, (8, 5, 24, 30), 2)
    pygame.draw.rect(save_surface, GREEN, (10, 7, 20, 26))
    pygame.draw.rect(save_surface, BLACK, (12, 5, 16, 8))
    pygame.image.save(save_surface, "save_icon.png")
    
    pygame.quit()
    
    print("✅ تم إنشاء جميع الأيقونات بنجاح:")
    print("   • wall_icon.png")
    print("   • door_icon.png") 
    print("   • window_icon.png")
    print("   • delete_icon.png")
    print("   • measure_icon.png")
    print("   • save_icon.png")

if __name__ == "__main__":
    create_simple_icons()
