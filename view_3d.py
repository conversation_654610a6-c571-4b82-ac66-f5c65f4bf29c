#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عارض التصميم ثلاثي الأبعاد
يستخدم Panda3D لعرض الجدران والعناصر في بيئة ثلاثية الأبعاد
"""

import json
import os
import sys
import math
from direct.showbase.ShowBase import ShowBase
from panda3d.core import (
    Point3, Vec3, VBase4, CardMaker, LineSegs,
    AmbientLight, DirectionalLight, 
    TransparencyAttrib
)

class Design3DViewer(ShowBase):
    def __init__(self, design_file_path=None):
        ShowBase.__init__(self)
        
        # إعداد الكاميرا
        self.disableMouse()
        self.camera_initial_pos = Point3(15, -25, 15)  # موضع أفضل لإظهار الجدران
        self.camera.setPos(self.camera_initial_pos)
        self.camera.lookAt(0, 0, 2)  # النظر إلى نقطة أعلى قليلاً
        
        # إعداد النافذة
        self.win.setClearColor(VBase4(0.8, 0.9, 1.0, 1))  # لون خلفية سماوي فاتح
        
        # إعداد الإضاءة
        self.setup_lights()
        
        # إعداد متغيرات التحكم بالكاميرا
        self.mouse_btn_down = [False, False, False]
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        # إعدادات الكاميرا
        self.default_cam_target = Point3(0, 0, 2)  # هدف أعلى قليلاً لإظهار الجدران بشكل أفضل
        self.cam_target = Point3(self.default_cam_target)
        self.cam_distance = 30
        self.cam_heading = 45
        self.cam_pitch = -20
        
        # إنشاء الأرضية
        self.create_floor()
        
        # إنشاء محاور الإحداثيات (مخفية افتراضياً)
        self.create_coordinate_axes()
        self.axes.hide()  # إخفاء المحاور كما طلب المستخدم
        
        # تحميل التصميم إذا تم توفير مسار الملف
        if design_file_path and os.path.exists(design_file_path):
            self.load_design(design_file_path)
        
        # تمكين التحكم التفاعلي
        self.enable_interactive_controls()
        
        # عرض التعليمات
        self.show_instructions()

    def setup_lights(self):
        """إعداد الإضاءة للمشهد"""
        # إضاءة محيطة أقوى لجعل الجدران أكثر وضوحًا
        alight = AmbientLight('alight')
        alight.setColor(VBase4(0.5, 0.5, 0.5, 1))
        alnp = self.render.attachNewNode(alight)
        self.render.setLight(alnp)
        
        # إضاءة اتجاهية رئيسية
        dlight = DirectionalLight('dlight')
        dlight.setColor(VBase4(0.9, 0.9, 0.9, 1))
        dlight.setDirection(Vec3(-1, -1, -1))
        dlnp = self.render.attachNewNode(dlight)
        self.render.setLight(dlnp)
        
        # إضاءة اتجاهية إضافية من الجانب الآخر
        dlight2 = DirectionalLight('dlight2')
        dlight2.setColor(VBase4(0.6, 0.6, 0.6, 1))
        dlight2.setDirection(Vec3(1, 1, -0.5))
        dlnp2 = self.render.attachNewNode(dlight2)
        self.render.setLight(dlnp2)

    def create_floor(self):
        """إنشاء أرضية شفافة"""
        cm = CardMaker("floor")
        cm.setFrame(-50, 50, -50, 50)
        floor_plane = self.render.attachNewNode(cm.generate())
        floor_plane.setP(-90)
        floor_plane.setPos(0, 0, 0)
        floor_plane.setColor(0.95, 0.95, 0.98, 0.4)  # لون أبيض فاتح شفاف لتباين أفضل
        floor_plane.setTransparency(TransparencyAttrib.MAlpha)

    def load_design(self, file_path):
        """تحميل التصميم من ملف JSON"""
        try:
            print(f"جاري محاولة تحميل التصميم من: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                design_data = json.load(f)
            
            print("تم قراءة ملف التصميم بنجاح")
            print(f"محتويات الملف: {design_data.keys()}")
            
            # تحميل الجدران
            if 'walls' in design_data:
                print(f"📐 عدد الجدران في الملف: {len(design_data['walls'])}")
                print("=" * 50)
                for i, wall in enumerate(design_data['walls']):
                    print(f"🧱 جدار رقم {i+1}:")
                    print(f"  📍 بداية: ({wall.get('start_m', [0,0])[0]:.2f}, {wall.get('start_m', [0,0])[1]:.2f})")
                    print(f"  📍 نهاية: ({wall.get('end_m', [0,0])[0]:.2f}, {wall.get('end_m', [0,0])[1]:.2f})")
                    print(f"  📏 الطول: {wall.get('length_m', 0):.2f} متر")
                    
                    # إضافة ارتفاع افتراضي إذا لم يكن موجودًا
                    if 'height_m' not in wall:
                        wall['height_m'] = 3.0
                        print(f"  📐 الارتفاع: {wall['height_m']:.2f} متر (افتراضي)")
                    else:
                        print(f"  📐 الارتفاع: {wall.get('height_m'):.2f} متر")
                    
                    self.create_3d_wall(wall)
                    print("-" * 30)
                print("=" * 50)
            else:
                print("⚠️ لا توجد جدران في ملف التصميم")
            
            print(f"تم تحميل التصميم من: {file_path}")
            
        except FileNotFoundError:
            print(f"خطأ: لم يتم العثور على الملف {file_path}")
        except json.JSONDecodeError as e:
            print(f"خطأ في قراءة ملف JSON: {e}")
        except Exception as e:
            print(f"خطأ عام في تحميل التصميم: {e}")

    def create_3d_wall(self, wall_data):
        """إنشاء جدار ثلاثي الأبعاد"""
        try:
            # استخراج البيانات
            start_m = wall_data.get('start_m', [0, 0])
            end_m = wall_data.get('end_m', [1, 0])
            wall_height = wall_data.get('height_m', 3.0)
            
            print(f"إنشاء جدار بارتفاع: {wall_height} متر")
            
            # تحويل الإحداثيات إلى نظام Panda3D
            start_3d = Point3(start_m[0], start_m[1], 0)
            end_3d = Point3(end_m[0], end_m[1], 0)
            
            # حساب طول الجدار واتجاهه
            wall_vec = end_3d - start_3d
            wall_length = wall_vec.length()
            wall_thickness = 0.25  # سمك الجدار بالمتر (زيادة السمك لجعله أكثر وضوحًا)
            
            if wall_length == 0:
                return
            
            # إنشاء عقدة الجدار
            wall_node = self.render.attachNewNode(f"wall")
            wall_node.setPos(start_3d)
            
            # حساب زاوية الدوران
            angle = math.atan2(wall_vec.y, wall_vec.x)
            wall_node.setH(math.degrees(angle))
            
            # تعيين لون رمادي واضح مشابه للصورة
            wall_color = (0.75, 0.75, 0.75, 1)  # لون رمادي واضح مثل الصورة
            
            # 1. الوجه الأمامي
            cm = CardMaker("front")
            cm.setFrame(0, wall_length, 0, wall_height)
            front = wall_node.attachNewNode(cm.generate())
            front.setY(-wall_thickness/2)
            front.setColor(*wall_color)
            
            # 2. الوجه الخلفي
            cm = CardMaker("back")
            cm.setFrame(0, wall_length, 0, wall_height)
            back = wall_node.attachNewNode(cm.generate())
            back.setY(wall_thickness/2)
            back.setH(180)
            back.setColor(*wall_color)
            
            # 3. الوجه العلوي
            cm = CardMaker("top")
            cm.setFrame(0, wall_length, -wall_thickness/2, wall_thickness/2)
            top = wall_node.attachNewNode(cm.generate())
            top.setZ(wall_height)
            top.setP(-90)
            top.setColor(0.85, 0.85, 0.85, 1)  # لون رمادي أفتح للوجه العلوي
            
            # 4. الوجه السفلي
            cm = CardMaker("bottom")
            cm.setFrame(0, wall_length, -wall_thickness/2, wall_thickness/2)
            bottom = wall_node.attachNewNode(cm.generate())
            bottom.setP(90)
            bottom.setColor(0.65, 0.65, 0.65, 1)  # لون رمادي أغمق للوجه السفلي
            
            # 5. الوجه الأيسر
            cm = CardMaker("left")
            cm.setFrame(-wall_thickness/2, wall_thickness/2, 0, wall_height)
            left = wall_node.attachNewNode(cm.generate())
            left.setH(-90)
            left.setColor(*wall_color)
            
            # 6. الوجه الأيمن
            cm = CardMaker("right")
            cm.setFrame(-wall_thickness/2, wall_thickness/2, 0, wall_height)
            right = wall_node.attachNewNode(cm.generate())
            right.setX(wall_length)
            right.setH(90)
            right.setColor(*wall_color)
            
            print(f"✓ تم إنشاء جدار بطول {wall_length:.2f} متر وارتفاع {wall_height:.2f} متر وسمك {wall_thickness:.2f} متر")
            print(f"  من النقطة ({start_m[0]:.2f}, {start_m[1]:.2f}) إلى ({end_m[0]:.2f}, {end_m[1]:.2f})")
            
            # إضافة خطوط الحواف للوضوح
            self.add_wall_edges(wall_node, wall_length, wall_height, wall_thickness)
            
        except Exception as e:
            print(f"خطأ في إنشاء الجدار ثلاثي الأبعاد: {e}")
            import traceback
            traceback.print_exc()

    def add_wall_edges(self, wall_node, length, height, thickness):
        """إضافة خطوط الحواف للجدار"""
        try:
            # إنشاء خطوط الحواف
            lines = LineSegs()
            lines.setThickness(3)  # سمك أكبر للخطوط لجعلها أكثر وضوحًا
            lines.setColor(0.2, 0.2, 0.2, 1)  # لون رمادي داكن للحواف
            
            # الحواف العمودية
            # الحافة الأمامية اليسرى
            lines.moveTo(0, -thickness/2, 0)
            lines.drawTo(0, -thickness/2, height)
            
            # الحافة الأمامية اليمنى
            lines.moveTo(length, -thickness/2, 0)
            lines.drawTo(length, -thickness/2, height)
            
            # الحافة الخلفية اليسرى
            lines.moveTo(0, thickness/2, 0)
            lines.drawTo(0, thickness/2, height)
            
            # الحافة الخلفية اليمنى
            lines.moveTo(length, thickness/2, 0)
            lines.drawTo(length, thickness/2, height)
            
            # الحواف الأفقية العلوية
            lines.moveTo(0, -thickness/2, height)
            lines.drawTo(length, -thickness/2, height)
            
            lines.moveTo(0, thickness/2, height)
            lines.drawTo(length, thickness/2, height)
            
            lines.moveTo(0, -thickness/2, height)
            lines.drawTo(0, thickness/2, height)
            
            lines.moveTo(length, -thickness/2, height)
            lines.drawTo(length, thickness/2, height)
            
            # إنشاء عقدة الخطوط وإضافتها للجدار
            edge_node = lines.create()
            wall_node.attachNewNode(edge_node)
            
        except Exception as e:
            print(f"خطأ في إضافة حواف الجدار: {e}")
