import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import hashlib
import os

# إنشاء قاعدة البيانات والجداول
def init_database():
    """إنشاء قاعدة البيانات والجداول المطلوبة"""
    conn = sqlite3.connect('app_database.db')
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            is_admin INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول بيانات العملاء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            project_name TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # إنشاء المستخدم الإداري الافتراضي
    admin_password = "Mfb112002*"
    password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
    
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password_hash, is_admin)
        VALUES (?, ?, 1)
    ''', ("MohammedBushiha", password_hash))
    
    conn.commit()
    conn.close()

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_user(username, password):
    """التحقق من بيانات المستخدم"""
    conn = sqlite3.connect('app_database.db')
    cursor = conn.cursor()
    
    password_hash = hash_password(password)
    cursor.execute('''
        SELECT id, username, is_admin FROM users 
        WHERE username = ? AND password_hash = ?
    ''', (username, password_hash))
    
    user = cursor.fetchone()
    conn.close()
    
    return user

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("تسجيل الدخول - نظام التصميم المعماري")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء واجهة تسجيل الدخول
        self.create_login_interface()
        
        # متغير لحفظ بيانات المستخدم المسجل
        self.current_user = None

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان
        title_label = ttk.Label(main_frame, text="تسجيل الدخول", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.username_entry = ttk.Entry(main_frame, width=25)
        self.username_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.password_entry = ttk.Entry(main_frame, width=25, show="*")
        self.password_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # زر تسجيل الدخول
        login_btn = ttk.Button(main_frame, text="تسجيل الدخول", command=self.login)
        login_btn.grid(row=3, column=0, columnspan=2, pady=20)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        user = verify_user(username, password)
        if user:
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'is_admin': user[2]
            }
            print(f"تم تسجيل الدخول بنجاح: {username}")
            self.root.destroy()
            self.show_dashboard()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        dashboard = DashboardWindow(self.current_user)
        dashboard.run()

    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.current_user

class DashboardWindow:
    def __init__(self, user):
        self.user = user
        self.root = tk.Tk()
        self.root.title(f"لوحة التحكم - مرحباً {user['username']}")
        self.root.geometry("600x400")
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء واجهة لوحة التحكم
        self.create_dashboard_interface()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_dashboard_interface(self):
        """إنشاء واجهة لوحة التحكم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان الترحيب
        welcome_label = ttk.Label(main_frame, 
                                text=f"مرحباً {self.user['username']}", 
                                font=("Arial", 18, "bold"))
        welcome_label.grid(row=0, column=0, columnspan=2, pady=(0, 30))
        
        # زر بدء مشروع جديد
        new_project_btn = ttk.Button(main_frame, 
                                   text="بدء مشروع جديد", 
                                   command=self.start_new_project,
                                   width=20)
        new_project_btn.grid(row=1, column=0, pady=10, padx=10)
        
        # زر إدارة العملاء
        manage_customers_btn = ttk.Button(main_frame, 
                                        text="إدارة العملاء", 
                                        command=self.manage_customers,
                                        width=20)
        manage_customers_btn.grid(row=1, column=1, pady=10, padx=10)
        
        # زر إدارة المستخدمين (للمدير فقط)
        if self.user['is_admin']:
            manage_users_btn = ttk.Button(main_frame, 
                                        text="إدارة المستخدمين", 
                                        command=self.manage_users,
                                        width=20)
            manage_users_btn.grid(row=2, column=0, pady=10, padx=10)
        
        # زر تسجيل الخروج
        logout_btn = ttk.Button(main_frame, 
                              text="تسجيل الخروج", 
                              command=self.logout,
                              width=20)
        logout_btn.grid(row=3, column=0, columnspan=2, pady=30)

    def start_new_project(self):
        """بدء مشروع جديد"""
        self.root.destroy()
        # استيراد وتشغيل تطبيق الرسم
        from main import start_drawing_app
        start_drawing_app()

    def manage_customers(self):
        """إدارة العملاء"""
        customer_window = CustomerManagementWindow(self.user)
        customer_window.run()

    def manage_users(self):
        """إدارة المستخدمين (للمدير فقط)"""
        if self.user['is_admin']:
            messagebox.showinfo("إدارة المستخدمين", "هذه الميزة قيد التطوير")
        else:
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول لهذه الميزة")

    def logout(self):
        """تسجيل الخروج"""
        self.root.destroy()
        # العودة لنافذة تسجيل الدخول
        start_customer_info()

    def run(self):
        """تشغيل لوحة التحكم"""
        self.root.mainloop()

class CustomerManagementWindow:
    def __init__(self, user):
        self.user = user
        self.root = tk.Tk()
        self.root.title("إدارة العملاء")
        self.root.geometry("800x600")
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء واجهة إدارة العملاء
        self.create_interface()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_interface(self):
        """إنشاء واجهة إدارة العملاء"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان
        title_label = ttk.Label(main_frame, text="إدارة العملاء", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # زر العودة
        back_btn = ttk.Button(main_frame, text="العودة", command=self.go_back)
        back_btn.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # هنا يمكن إضافة المزيد من الوظائف لإدارة العملاء
        info_label = ttk.Label(main_frame, text="ميزة إدارة العملاء قيد التطوير")
        info_label.grid(row=2, column=0, columnspan=2, pady=50)

    def go_back(self):
        """العودة للوحة التحكم"""
        self.root.destroy()

    def run(self):
        """تشغيل نافذة إدارة العملاء"""
        self.root.mainloop()

def start_customer_info():
    """نقطة البداية للتطبيق"""
    # إنشاء قاعدة البيانات
    init_database()
    
    # تشغيل نافذة تسجيل الدخول
    login_window = LoginWindow()
    user = login_window.run()
    
    return user

if __name__ == "__main__":
    start_customer_info()
