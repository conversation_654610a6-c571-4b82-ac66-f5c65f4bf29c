name: All Builds (Linux, macOS, Windows, Android, IPA, APK, AAB)

on:
  # Runs on push to any of the below branches
  push:
    branches:
      - master
      - main
  # Runs on pull request events that target one of the below branches
  pull_request:
    branches:
      - master
      - main

  # Allows you to run this workflow manually from the Actions tab of the repository
  workflow_dispatch:

env:
  # https://flet.dev/docs/publish#versioning
  BUILD_NUMBER: 1
  BUILD_VERSION: 1.0.0
  PYTHON_VERSION: 3.12.2
  FLUTTER_VERSION: 3.22.2

jobs:
  build-linux:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Patch for linux build
      run: |
        flutter doctor
        sudo apt-get update -y
        sudo apt-get install -y ninja-build libgtk-3-dev
        flutter doctor

    - name: Flet Build Linux
      run: |
        flutter config --no-analytics 
        flet build linux --verbose --build-number=$BUILD_NUMBER --build-version=$BUILD_VERSION

    - name: Upload Linux Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: linux-build-artifact
        path: build/linux
        if-no-files-found: warn
        overwrite: false

  build-macos:
    runs-on: macos-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Flet Build macOS
      run: |
        flutter config --no-analytics
        flet build macos --verbose --build-number=$BUILD_NUMBER --build-version=$BUILD_VERSION

    - name: Upload macOS Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: macos-build-artifact
        path: build/macos
        if-no-files-found: warn
        overwrite: false

  build-windows:
    runs-on: windows-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Flet Build Windows
      run: |
        flutter config --no-analytics
        flet build windows --verbose --no-rich-output --build-number=$env:BUILD_NUMBER --build-version=$env:BUILD_VERSION

    - name: Upload Windows Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: windows-build-artifact
        path: build/windows
        if-no-files-found: warn
        overwrite: false

  build-aab:
    runs-on: macos-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Flet Build AAB
      run: |
        flutter config --no-analytics
        flet build aab --verbose --build-number=$BUILD_NUMBER --build-version=$BUILD_VERSION

    - name: Upload AAB Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: aab-build-artifact
        path: build/aab
        if-no-files-found: warn
        overwrite: false

  build-apk:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Setup Java JDK
      uses: actions/setup-java@v4.2.1
      with:
        distribution: 'temurin'
        java-version: '21'

    - name: Flet Build APK
      run: |
        flutter config --no-analytics
        flet build apk --verbose --build-number=$BUILD_NUMBER --build-version=$BUILD_VERSION

    - name: Upload APK Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: apk-build-artifact
        path: build/apk
        if-no-files-found: warn
        overwrite: false

  build-ipa:
    runs-on: macos-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup Flutter ${{ env.FLUTTER_VERSION }}
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Flet Build IPA
      run: |
        flutter config --no-analytics
        flet build ipa --verbose --build-number=$BUILD_NUMBER --build-version=$BUILD_VERSION 

    - name: Upload IPA Artifact
      uses: actions/upload-artifact@v4.3.4
      with:
        name: ipa-build-artifact
        path: build/ipa
        if-no-files-found: warn
        overwrite: fals
